/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useState } from 'react';
import { generateErrorToast, generateToast } from '@g17eco/molecules/toasts';
import { Button } from 'reactstrap';
import { DashboardRow, DashboardSection } from '@g17eco/molecules/dashboard';
import { CreateReportDocumentMin, ReportDocument, ReportDocumentTemplate } from '@g17eco/types/reportDocument';
import { useCreateReportDocumentMutation, useUpdateReportDocumentMutation } from '@api/initiative-report-documents';
import { getGroup } from '@g17eco/core';
import { FieldProps, FormGenerator } from '@g17eco/molecules/form';
import { SubmitButton } from '@g17eco/molecules/button/SubmitButton';

interface Props {
  initiativeId: string;
  type: string;
  onCancel: () => void;
  onView: (reportId: string) => void;
  reportDocument?: ReportDocument;
}

const fields = [
  {
    code: 'title',
    label: 'Title',
    type: 'text',
    required: true,
  },
  {
    code: 'description',
    label: 'Description',
    type: 'textarea',
  },
  {
    parentCode: 'config',
    code: 'template',
    label: 'Template',
    type: 'select',
    required: true,
    options: [
      { value: ReportDocumentTemplate.Blank, label: 'Blank' },
      { value: ReportDocumentTemplate.Simple, label: 'Simple' },
      { value: ReportDocumentTemplate.AiGenerated, label: 'AI Generated' },
    ],
    testId: 'template-type-select',
  },
] satisfies FieldProps<CreateReportDocumentMin>[];

const getInitialForm = ({ initiativeId, type }: { initiativeId: string; type: string }): CreateReportDocumentMin => ({
  title: '',
  description: '',
  initiativeId,
  type,
  config: {
    template: ReportDocumentTemplate.Blank,
  },
});

export const ReportDocumentCreate = (props: Props) => {
  const { initiativeId, type, onCancel, onView, reportDocument } = props;

  const [form, setForm] = useState<CreateReportDocumentMin | ReportDocument>(
    reportDocument ?? getInitialForm({ initiativeId, type }),
  );

  const [createCustomReport, { isLoading: isCreating }] = useCreateReportDocumentMutation();
  const [updateCustomReport, { isLoading: isUpdating }] = useUpdateReportDocumentMutation();

  const isDisabled = !form.title || !form.type || isCreating || isUpdating;
  const reportId = form._id;

  const callbackSubmit = (reportDocument: ReportDocument) => {
    if (reportId) {
      onCancel();
      return;
    }
    setForm(reportDocument);
    onView(reportDocument._id);
  };

  const handleSubmit = async () => {
    if (isDisabled) {
      return;
    }

    const action = reportId ? 'updated' : 'created';
    const method = reportId && 'created' in form ? updateCustomReport(form) : createCustomReport(form);

    return method
      .unwrap()
      .then((reportDocument) => {
        generateToast({
          title: `Custom report ${action}`,
          color: 'success',
          message: `Custom report template has been ${action}`,
        });
        callbackSubmit(reportDocument);
      })
      .catch((error) => {
        generateErrorToast(error);
      });
  };

  const buttonText = reportId ? 'Save' : 'Create report';
  const group = getGroup('standards', type);
  return (
    <>
      <DashboardRow>
        <Button type='button' color='link' onClick={onCancel}>
          <i className='fas fa-arrow-left mr-2'></i>
          Back
        </Button>
      </DashboardRow>
      <DashboardRow>
        <div className='w-100 d-flex justify-content-between align-items-center'>
          <h3>{group?.name ?? type} Report</h3>
        </div>
      </DashboardRow>
      <DashboardSection paddingInternal={0}>
        <FormGenerator
          fields={fields}
          form={form}
          updateForm={(update) => {
            const { name, value } = update.currentTarget;

            const field = fields.find((field) => field.code === name);
            if (!field) {
              return;
            }
            const newFormData: any = { ...form };
            if (field.parentCode) {
              newFormData[field.parentCode][name] = value;
              setForm((currentForm) => ({ ...currentForm, [field.parentCode][code]: value }));
            } else {
              newFormData[code] = value;
            }

            setForm((currentForm) => ({ ...currentForm, [name]: value }));
          }}
        />
        <div className='d-flex flex-row justify-content-between'>
          <div className='mt-3 d-flex justify-content-end gap-3'>
            <Button color='link-secondary' onClick={onCancel}>
              Cancel
            </Button>
            <SubmitButton color='primary' onClick={handleSubmit} disabled={isDisabled}>
              {buttonText}
            </SubmitButton>
          </div>
        </div>
      </DashboardSection>
    </>
  );
};
